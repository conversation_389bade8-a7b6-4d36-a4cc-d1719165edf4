#!/usr/bin/env python3
"""
Fix data corruption issues identified by cross-checking with repository data
"""

import pandas as pd
import sqlite3
from datetime import datetime
import sys

def load_repository_data():
    """Load authoritative repository data"""
    print("Loading repository data...")
    
    # Load events
    events_df = pd.read_csv('scrape_ufc_stats/ufc_event_details.csv')
    events_df['DATE_PARSED'] = pd.to_datetime(events_df['DATE'], format='%B %d, %Y')
    cutoff_date = datetime(2015, 7, 1)
    events_df = events_df[events_df['DATE_PARSED'] >= cutoff_date].copy()
    
    # Load fight results
    results_df = pd.read_csv('scrape_ufc_stats/ufc_fight_results.csv')
    
    # Clean event names (remove trailing spaces)
    events_df['EVENT'] = events_df['EVENT'].str.strip()
    results_df['EVENT'] = results_df['EVENT'].str.strip()
    
    # Filter fight results to only include events from our date range
    valid_events = set(events_df['EVENT'].values)
    results_df = results_df[results_df['EVENT'].isin(valid_events)].copy()
    
    print(f"Repository: {len(events_df)} events, {len(results_df)} fights")
    return events_df, results_df

def fix_specific_corrupted_fights():
    """Fix specific fights with known data corruption"""
    print("\nFixing specific corrupted fights...")
    
    conn = sqlite3.connect('ufc_data.db')
    cursor = conn.cursor()
    
    # List of known corrupted fights to fix
    corrupted_fights = [
        {
            'description': 'UFC 299: Sean O\'Malley vs. Marlon Vera',
            'bout_pattern': '%Sean O\'Malley%Marlon Vera%',
            'event_pattern': '%UFC 299%',
            'correct_method': 'Decision - Unanimous',
            'correct_round': 5,
            'correct_time': '5:00'
        },
        # Add more corrupted fights as they are discovered
    ]
    
    for fight in corrupted_fights:
        print(f"  Fixing: {fight['description']}")
        
        cursor.execute("""
            UPDATE fights 
            SET result_method = ?,
                result_round = ?,
                result_time = ?
            WHERE bout LIKE ?
              AND event_id = (SELECT id FROM events WHERE event_name LIKE ?)
        """, (
            fight['correct_method'],
            fight['correct_round'], 
            fight['correct_time'],
            fight['bout_pattern'],
            fight['event_pattern']
        ))
        
        if cursor.rowcount > 0:
            print(f"    ✅ Fixed {cursor.rowcount} record(s)")
        else:
            print(f"    ⚠️  No records found to fix")
    
    conn.commit()
    conn.close()

def find_and_fix_systematic_corruption(repo_results):
    """Find and fix systematic data corruption by comparing with repository"""
    print("\nFinding systematic data corruption...")
    
    conn = sqlite3.connect('ufc_data.db')
    
    # Load database fights
    db_fights = pd.read_sql_query("""
        SELECT f.id, f.bout, e.event_name, f.weight_class, f.result_method, 
               f.result_round, f.result_time, f.winner_id,
               f1.first_name || ' ' || f1.last_name as fighter1_name,
               f2.first_name || ' ' || f2.last_name as fighter2_name
        FROM fights f
        JOIN events e ON f.event_id = e.id
        LEFT JOIN fighters f1 ON f.fighter1_id = f1.id
        LEFT JOIN fighters f2 ON f.fighter2_id = f2.id
        WHERE f.fight_status = 'valid'
        ORDER BY e.date, f.id
    """, conn)
    
    corrupted_fights = []
    
    # Check recent events for corruption
    recent_events = ['UFC 300', 'UFC 299', 'UFC 298', 'UFC 297', 'UFC 296', 'UFC 295']
    
    for event_name in recent_events:
        print(f"  Checking {event_name}...")
        
        # Get fights from this event in both sources
        repo_event_fights = repo_results[repo_results['EVENT'].str.contains(event_name, na=False)]
        db_event_fights = db_fights[db_fights['event_name'].str.contains(event_name, na=False)]
        
        # Compare each fight
        for _, repo_fight in repo_event_fights.iterrows():
            bout_parts = repo_fight['BOUT'].split(' vs. ')
            if len(bout_parts) == 2:
                fighter1 = bout_parts[0].strip()
                fighter2 = bout_parts[1].strip()
                
                # Look for this fight in database
                db_match = db_event_fights[
                    (db_event_fights['bout'].str.contains(fighter1, na=False, case=False)) &
                    (db_event_fights['bout'].str.contains(fighter2, na=False, case=False))
                ]
                
                if not db_match.empty:
                    db_fight = db_match.iloc[0]
                    
                    # Check for significant discrepancies
                    method_mismatch = repo_fight['METHOD'].strip() != db_fight['result_method']
                    round_mismatch = str(repo_fight['ROUND']) != str(db_fight['result_round'])
                    time_mismatch = repo_fight['TIME'] != db_fight['result_time']
                    
                    # Only flag as corruption if there are significant mismatches
                    if (method_mismatch and round_mismatch) or (round_mismatch and time_mismatch):
                        corrupted_fights.append({
                            'fight_id': db_fight['id'],
                            'bout': db_fight['bout'],
                            'event': event_name,
                            'db_method': db_fight['result_method'],
                            'repo_method': repo_fight['METHOD'].strip(),
                            'db_round': db_fight['result_round'],
                            'repo_round': repo_fight['ROUND'],
                            'db_time': db_fight['result_time'],
                            'repo_time': repo_fight['TIME']
                        })
    
    # Fix the corrupted fights
    if corrupted_fights:
        print(f"\n  Found {len(corrupted_fights)} corrupted fights to fix:")
        
        cursor = conn.cursor()
        for fight in corrupted_fights:
            print(f"    Fixing: {fight['bout']} ({fight['event']})")
            print(f"      Method: {fight['db_method']} → {fight['repo_method']}")
            print(f"      Round: {fight['db_round']} → {fight['repo_round']}")
            print(f"      Time: {fight['db_time']} → {fight['repo_time']}")
            
            cursor.execute("""
                UPDATE fights 
                SET result_method = ?,
                    result_round = ?,
                    result_time = ?
                WHERE id = ?
            """, (
                fight['repo_method'],
                fight['repo_round'],
                fight['repo_time'],
                fight['fight_id']
            ))
        
        conn.commit()
        print(f"    ✅ Fixed {len(corrupted_fights)} corrupted fights")
    else:
        print("  ✅ No systematic corruption found")
    
    conn.close()
    return len(corrupted_fights)

def verify_fixes():
    """Verify that the fixes were applied correctly"""
    print("\nVerifying fixes...")
    
    conn = sqlite3.connect('ufc_data.db')
    
    # Check for impossible decisions (decisions ending before round 3)
    cursor = conn.cursor()
    cursor.execute("""
        SELECT COUNT(*) 
        FROM fights 
        WHERE result_method LIKE '%Decision%' 
          AND result_round < 3 
          AND fight_status = 'valid'
    """)
    
    impossible_decisions = cursor.fetchone()[0]
    
    if impossible_decisions == 0:
        print("  ✅ No impossible decisions found")
    else:
        print(f"  ❌ Still have {impossible_decisions} impossible decisions")
    
    # Check a few specific fights
    test_fights = [
        ('UFC 236', 'Dustin Poirier', 'Max Holloway', 'Decision - Unanimous', 5, '5:00'),
        ('UFC 299', 'Sean O\'Malley', 'Marlon Vera', 'Decision - Unanimous', 5, '5:00')
    ]
    
    for event, fighter1, fighter2, expected_method, expected_round, expected_time in test_fights:
        cursor.execute("""
            SELECT f.result_method, f.result_round, f.result_time
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE e.event_name LIKE ?
              AND f.bout LIKE ?
              AND f.bout LIKE ?
        """, (f'%{event}%', f'%{fighter1}%', f'%{fighter2}%'))
        
        result = cursor.fetchone()
        if result:
            method, round_num, time = result
            if method == expected_method and round_num == expected_round and time == expected_time:
                print(f"  ✅ {event}: {fighter1} vs {fighter2} - Correct")
            else:
                print(f"  ❌ {event}: {fighter1} vs {fighter2} - Still incorrect")
                print(f"      Expected: {expected_method} R{expected_round} {expected_time}")
                print(f"      Actual: {method} R{round_num} {time}")
        else:
            print(f"  ⚠️  {event}: {fighter1} vs {fighter2} - Fight not found")
    
    conn.close()

def main():
    """Main function to fix data corruption"""
    print("UFC Data Corruption Fix Tool")
    print("=" * 50)
    
    try:
        # Load repository data
        repo_events, repo_results = load_repository_data()
        
        # Fix specific known corrupted fights
        fix_specific_corrupted_fights()
        
        # Find and fix systematic corruption
        fixes_applied = find_and_fix_systematic_corruption(repo_results)
        
        # Verify fixes
        verify_fixes()
        
        print("\n" + "=" * 50)
        print(f"Data corruption fix complete! Applied {fixes_applied} systematic fixes.")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
