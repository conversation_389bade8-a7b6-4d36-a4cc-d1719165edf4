#!/usr/bin/env python3
"""
Cross-check UFC database against authoritative repository data
"""

import pandas as pd
import sqlite3
from datetime import datetime
import sys

def load_repository_data():
    """Load and filter repository data from July 1, 2015 onwards"""
    print("Loading repository data...")

    # Load events
    events_df = pd.read_csv('scrape_ufc_stats/ufc_event_details.csv')
    events_df['DATE_PARSED'] = pd.to_datetime(events_df['DATE'], format='%B %d, %Y')
    cutoff_date = datetime(2015, 7, 1)
    events_df = events_df[events_df['DATE_PARSED'] >= cutoff_date].copy()

    # Load fight results
    results_df = pd.read_csv('scrape_ufc_stats/ufc_fight_results.csv')

    # Clean event names (remove trailing spaces)
    events_df['EVENT'] = events_df['EVENT'].str.strip()
    results_df['EVENT'] = results_df['EVENT'].str.strip()

    # Filter fight results to only include events from our date range
    valid_events = set(events_df['EVENT'].values)
    results_df = results_df[results_df['EVENT'].isin(valid_events)].copy()

    print(f"Repository: {len(events_df)} events, {len(results_df)} fights")
    return events_df, results_df

def load_database_data():
    """Load data from SQLite database"""
    print("Loading database data...")
    
    conn = sqlite3.connect('ufc_data.db')
    
    # Load events
    db_events = pd.read_sql_query("""
        SELECT id, event_name, date, location 
        FROM events 
        ORDER BY date
    """, conn)
    
    # Load fights with fighter names
    db_fights = pd.read_sql_query("""
        SELECT f.id, f.bout, e.event_name, f.weight_class, f.result_method, 
               f.result_round, f.result_time, f.winner_id,
               f1.first_name || ' ' || f1.last_name as fighter1_name,
               f2.first_name || ' ' || f2.last_name as fighter2_name,
               fw.first_name || ' ' || fw.last_name as winner_name
        FROM fights f
        JOIN events e ON f.event_id = e.id
        LEFT JOIN fighters f1 ON f.fighter1_id = f1.id
        LEFT JOIN fighters f2 ON f.fighter2_id = f2.id
        LEFT JOIN fighters fw ON f.winner_id = fw.id
        WHERE f.fight_status = 'valid'
        ORDER BY e.date, f.id
    """, conn)
    
    conn.close()
    print(f"Database: {len(db_events)} events, {len(db_fights)} fights")
    return db_events, db_fights

def compare_events(repo_events, db_events):
    """Compare event data between repository and database"""
    print("\n=== COMPARING EVENTS ===")
    
    # Create comparable event names
    repo_events['EVENT_CLEAN'] = repo_events['EVENT'].str.strip()
    db_events['EVENT_CLEAN'] = db_events['event_name'].str.strip()
    
    repo_event_names = set(repo_events['EVENT_CLEAN'].values)
    db_event_names = set(db_events['EVENT_CLEAN'].values)
    
    missing_in_db = repo_event_names - db_event_names
    extra_in_db = db_event_names - repo_event_names
    
    print(f"Events in repository: {len(repo_event_names)}")
    print(f"Events in database: {len(db_event_names)}")
    print(f"Missing from database: {len(missing_in_db)}")
    print(f"Extra in database: {len(extra_in_db)}")
    
    if missing_in_db:
        print("\nMISSING FROM DATABASE:")
        for event in sorted(missing_in_db)[:10]:  # Show first 10
            print(f"  - {event}")
        if len(missing_in_db) > 10:
            print(f"  ... and {len(missing_in_db) - 10} more")
    
    if extra_in_db:
        print("\nEXTRA IN DATABASE:")
        for event in sorted(extra_in_db)[:10]:  # Show first 10
            print(f"  - {event}")
        if len(extra_in_db) > 10:
            print(f"  ... and {len(extra_in_db) - 10} more")

def compare_fight_results(repo_results, db_fights):
    """Compare fight results between repository and database"""
    print("\n=== COMPARING FIGHT RESULTS ===")

    # Focus on a specific fight to check data integrity
    # Let's check the Max Holloway vs Dustin Poirier fight we fixed
    print("\nChecking UFC 236: Poirier vs Holloway fight...")

    # Repository data for this fight
    repo_fight = repo_results[
        (repo_results['EVENT'].str.contains('UFC 236', na=False)) &
        (repo_results['BOUT'].str.contains('Dustin Poirier', na=False) &
         repo_results['BOUT'].str.contains('Max Holloway', na=False))
    ]

    # Database data for this fight
    db_fight = db_fights[
        (db_fights['event_name'].str.contains('UFC 236', na=False)) &
        (db_fights['bout'].str.contains('Dustin Poirier', na=False) &
         db_fights['bout'].str.contains('Max Holloway', na=False))
    ]

    if not repo_fight.empty and not db_fight.empty:
        print("REPOSITORY DATA:")
        print(f"  Event: {repo_fight.iloc[0]['EVENT']}")
        print(f"  Bout: {repo_fight.iloc[0]['BOUT']}")
        print(f"  Weight Class: {repo_fight.iloc[0]['WEIGHTCLASS']}")
        print(f"  Method: {repo_fight.iloc[0]['METHOD']}")
        print(f"  Round: {repo_fight.iloc[0]['ROUND']}")
        print(f"  Time: {repo_fight.iloc[0]['TIME']}")

        print("\nDATABASE DATA:")
        print(f"  Event: {db_fight.iloc[0]['event_name']}")
        print(f"  Bout: {db_fight.iloc[0]['bout']}")
        print(f"  Weight Class: {db_fight.iloc[0]['weight_class']}")
        print(f"  Method: {db_fight.iloc[0]['result_method']}")
        print(f"  Round: {db_fight.iloc[0]['result_round']}")
        print(f"  Time: {db_fight.iloc[0]['result_time']}")

        # Check for discrepancies (normalize weight class comparison)
        discrepancies = []
        repo_weight = repo_fight.iloc[0]['WEIGHTCLASS']
        db_weight = db_fight.iloc[0]['weight_class']

        # Normalize weight class names for comparison
        if 'Lightweight' in repo_weight and db_weight == 'Lightweight':
            weight_match = True
        else:
            weight_match = repo_weight == db_weight

        if not weight_match:
            discrepancies.append(f"Weight class: Repo='{repo_weight}' vs DB='{db_weight}'")
        if repo_fight.iloc[0]['METHOD'] != db_fight.iloc[0]['result_method']:
            discrepancies.append(f"Method: Repo='{repo_fight.iloc[0]['METHOD']}' vs DB='{db_fight.iloc[0]['result_method']}'")
        if str(repo_fight.iloc[0]['ROUND']) != str(db_fight.iloc[0]['result_round']):
            discrepancies.append(f"Round: Repo='{repo_fight.iloc[0]['ROUND']}' vs DB='{db_fight.iloc[0]['result_round']}'")
        if repo_fight.iloc[0]['TIME'] != db_fight.iloc[0]['result_time']:
            discrepancies.append(f"Time: Repo='{repo_fight.iloc[0]['TIME']}' vs DB='{db_fight.iloc[0]['result_time']}'")

        if discrepancies:
            print("\n❌ DISCREPANCIES FOUND:")
            for disc in discrepancies:
                print(f"  - {disc}")
        else:
            print("\n✅ Data matches!")
    else:
        print("❌ Fight not found in one or both sources")
        if repo_fight.empty:
            print("  Repository: Fight not found")
        if db_fight.empty:
            print("  Database: Fight not found")

def compare_sample_fights(repo_results, db_fights):
    """Compare a sample of fights to identify systematic issues"""
    print("\n=== COMPARING SAMPLE FIGHTS ===")

    # Get a sample of fights from different events
    sample_events = ['UFC 300', 'UFC 299', 'UFC 298', 'UFC 297', 'UFC 296']
    discrepancies_found = []

    for event_name in sample_events:
        print(f"\nChecking {event_name}...")

        # Get fights from this event in both sources
        repo_event_fights = repo_results[repo_results['EVENT'].str.contains(event_name, na=False)]
        db_event_fights = db_fights[db_fights['event_name'].str.contains(event_name, na=False)]

        print(f"  Repository: {len(repo_event_fights)} fights")
        print(f"  Database: {len(db_event_fights)} fights")

        # Compare a few fights from this event
        for _, repo_fight in repo_event_fights.head(3).iterrows():
            # Try to find matching fight in database
            bout_parts = repo_fight['BOUT'].split(' vs. ')
            if len(bout_parts) == 2:
                fighter1 = bout_parts[0].strip()
                fighter2 = bout_parts[1].strip()

                # Look for this fight in database
                db_match = db_event_fights[
                    (db_event_fights['bout'].str.contains(fighter1, na=False)) &
                    (db_event_fights['bout'].str.contains(fighter2, na=False))
                ]

                if not db_match.empty:
                    db_fight = db_match.iloc[0]

                    # Compare key fields
                    issues = []
                    if repo_fight['METHOD'] != db_fight['result_method']:
                        issues.append(f"Method: {repo_fight['METHOD']} vs {db_fight['result_method']}")
                    if str(repo_fight['ROUND']) != str(db_fight['result_round']):
                        issues.append(f"Round: {repo_fight['ROUND']} vs {db_fight['result_round']}")
                    if repo_fight['TIME'] != db_fight['result_time']:
                        issues.append(f"Time: {repo_fight['TIME']} vs {db_fight['result_time']}")

                    if issues:
                        discrepancies_found.append({
                            'event': event_name,
                            'bout': repo_fight['BOUT'],
                            'issues': issues
                        })

    if discrepancies_found:
        print(f"\n❌ Found {len(discrepancies_found)} fights with discrepancies:")
        for disc in discrepancies_found[:10]:  # Show first 10
            print(f"  {disc['event']}: {disc['bout']}")
            for issue in disc['issues']:
                print(f"    - {issue}")
    else:
        print("\n✅ No discrepancies found in sample")

def find_systematic_issues(repo_results, db_fights):
    """Look for systematic data quality issues"""
    print("\n=== LOOKING FOR SYSTEMATIC ISSUES ===")

    # Check for impossible decision results (decisions ending before round 3)
    print("\n1. Checking for impossible decision results...")
    impossible_decisions = db_fights[
        (db_fights['result_method'].str.contains('Decision', na=False)) &
        (db_fights['result_round'] < 3)
    ]

    if not impossible_decisions.empty:
        print(f"❌ Found {len(impossible_decisions)} impossible decisions (ending before round 3):")
        for _, fight in impossible_decisions.head(5).iterrows():
            print(f"  - {fight['bout']} ({fight['event_name']}): {fight['result_method']} in round {fight['result_round']}")
    else:
        print("✅ No impossible decisions found")

    # Check for missing weight classes
    print("\n2. Checking for missing weight classes...")
    missing_weight_class = db_fights[db_fights['weight_class'].isna() | (db_fights['weight_class'] == '')]
    print(f"Fights with missing weight class: {len(missing_weight_class)}")

    # Check for unusual result methods
    print("\n3. Checking result method distribution...")
    result_methods = db_fights['result_method'].value_counts()
    print("Top result methods:")
    for method, count in result_methods.head(10).items():
        print(f"  {method}: {count}")

    # Look for potential data corruption patterns
    unusual_methods = result_methods[result_methods < 5]
    if not unusual_methods.empty:
        print(f"\nUnusual result methods (< 5 occurrences): {len(unusual_methods)}")
        for method, count in unusual_methods.items():
            print(f"  {method}: {count}")

    # Check for fights with very short times but not quick finishes
    print("\n4. Checking for suspicious fight times...")
    suspicious_times = db_fights[
        (db_fights['result_time'].str.contains('0:', na=False)) &
        (~db_fights['result_method'].str.contains('KO|TKO|Submission', na=False))
    ]

    if not suspicious_times.empty:
        print(f"❌ Found {len(suspicious_times)} fights with suspicious short times:")
        for _, fight in suspicious_times.head(5).iterrows():
            print(f"  - {fight['bout']}: {fight['result_method']} at {fight['result_time']}")
    else:
        print("✅ No suspicious fight times found")

def main():
    """Main cross-checking function"""
    print("UFC Database Cross-Check Tool")
    print("=" * 50)
    
    try:
        # Load data
        repo_events, repo_results = load_repository_data()
        db_events, db_fights = load_database_data()
        
        # Run comparisons
        compare_events(repo_events, db_events)
        compare_fight_results(repo_results, db_fights)
        compare_sample_fights(repo_results, db_fights)
        find_systematic_issues(repo_results, db_fights)
        
        print("\n" + "=" * 50)
        print("Cross-check complete!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
